/**
 * 聊天室页面 - 重写版
 */
import API from '../../../utils/api.js';
const imageOptimizer = require('../../../utils/imageOptimizer');

const app = getApp();

Page({
  data: {
    roomId: '',
    orderNo: '',
    messageList: [],
    inputText: '',
    canSend: false, // 添加一个明确的发送状态标志
    loading: false,
    page: 1,
    pageSize: 20,
    hasMore: true,
    userInfo: null,
    chatRoomInfo: null,
    scrollToView: '',
    scrollTop: 0, // 滚动位置
    themeLoaded: true, // 立即设置主题加载状态，避免白色背景闪烁
    messageWatcher: null, // 消息监听器
    isWatcherActive: false, // 监听器状态
    // 新增的UI状态
    showAttachmentPanel: false, // 附件面板显示状态
    showEmojiPanel: false, // 表情包面板显示状态
    // 表情包数据
    emojiList: [
      // 常用表情
      { category: '常用', emojis: ['😊', '😂', '🥰', '😍', '🤔', '😅', '😭', '😘', '🙄', '😴', '🤗', '🥺'] },
      // 人物表情
      { category: '人物', emojis: ['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙', '🥲', '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '🥸', '😎', '🤓', '🧐'] },
      // 手势
      { category: '手势', emojis: ['👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '🤲', '🤝', '🙏'] },
      // 物品
      { category: '物品', emojis: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓'] }
    ]
  },

  // 格式化聊天消息时间
  formatMessageTime(dateTime) {
    if (!dateTime) return '';

    try {
      const time = new Date(dateTime);

      // 检查日期是否有效
      if (isNaN(time.getTime())) {
        return '';
      }

      // 格式化为 YYYY-M-D-HH:mm 格式
      const year = time.getFullYear();
      const month = time.getMonth() + 1; // 不补零
      const day = time.getDate(); // 不补零
      const hour = String(time.getHours()).padStart(2, '0'); // 补零
      const minute = String(time.getMinutes()).padStart(2, '0'); // 补零

      return `${year}-${month}-${day}-${hour}:${minute}`;
    } catch (error) {
      console.error('时间格式化失败:', error);
      return '';
    }
  },

  onLoad(options) {
    // 🚫 激活加载消除器 - 彻底消除"加载中"提示
    const { activateForChat, forceHideAll } = require('../../../utils/loadingKiller');
    activateForChat();
    forceHideAll();

    // 立即设置主题加载状态，避免白色背景闪烁
    this.setData({ themeLoaded: true });

    const { roomId, orderNo, orderId } = options;

    // 如果有roomId，直接使用
    if (roomId) {
      this.setData({
        roomId: roomId,
        orderNo: orderNo || ''
      });

      // 设置当前活跃的聊天室（这会自动清除该聊天室的未读数）
      app.globalData.setActiveChatRoom(roomId);

      this.initUserInfo();
      this.initImageOptimizer();
      this.loadMessages(true).then(() => {
        // 初始消息加载完成后启动监听器
        this.startMessageWatcher();
      });
    }
    // 如果只有orderId，尝试查找或创建聊天室
    else if (orderId) {
      this.findOrCreateChatRoom(orderId);
    }
    // 参数不完整
    else {
      wx.showToast({
        title: '聊天室参数无效',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
  },

  onReady() {
    console.log('=== 聊天室页面就绪 ===');

    // 页面就绪后延迟滚动到底部
    setTimeout(() => {
      this.scrollToBottom();
    }, 500);
  },

  onShow() {
    // 🚫 确保加载消除器仍然活跃
    const { activateForChat, forceHideAll } = require('../../../utils/loadingKiller');
    activateForChat();
    forceHideAll();

    // 页面显示时滚动到底部（延迟执行确保页面完全加载）
    setTimeout(() => {
      this.scrollToBottom();
    }, 200);

    // 如果监听器未启动，则启动监听器
    if (!this.data.isWatcherActive) {
      this.startMessageWatcher();
    }
  },

  onHide() {
    // 页面隐藏时停止监听器（节省资源）
    console.log('📱 [页面状态] 页面隐藏，停止监听器');
    this.stopMessageWatcher();
  },

  // 通过订单ID查找或创建聊天室
  async findOrCreateChatRoom(orderId) {
    try {
      wx.showLoading({
        title: '查找聊天室...',
        mask: true
      });

      // 调用API查找聊天室
      const result = await API.createChatRoom(orderId);

      if (result.success && result.data.chatRoomId) {
        // 设置聊天室ID并初始化
        this.setData({
          roomId: result.data.chatRoomId,
          orderNo: orderId
        });

        this.initUserInfo();
        this.loadMessages(true).then(() => {
          // 初始消息加载完成后启动监听器
          this.startMessageWatcher();
        });
      } else {
        wx.showToast({
          title: result.error || '聊天室不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('❌ [聊天室] 查找异常:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      wx.hideLoading();
    }
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
      console.log('✅ [用户信息] 用户:', userInfo._id, userInfo.nickName);
    } else {
      console.log('❌ [用户信息] 用户信息不存在');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载消息列表（优化版）
  async loadMessages(refresh = false) {
    if (this.data.loading) {
      console.log('⚠️ [消息加载] 正在加载中，跳过重复请求');
      return Promise.resolve();
    }

    try {
      // 确保隐藏任何可能的系统加载提示
      wx.hideLoading();
      this.setData({ loading: true });

      const page = refresh ? 1 : this.data.page;
      const cacheKey = `${this.data.roomId}_page_${page}`;

      console.log(`🔄 [消息加载] 加载消息 - 页码: ${page}, 刷新: ${refresh}`);

      // 检查缓存（仅对历史消息使用缓存）
      if (!refresh && page > 1) {
        const cachedMessages = this.getMessageCache(cacheKey);
        if (cachedMessages) {
          console.log('📦 [消息缓存] 使用缓存数据');
          this.processCachedMessages(cachedMessages, page);
          return Promise.resolve();
        }
      }

      // 禁用系统加载提示，使用页面内的加载状态
      const result = await API.callFunction('chatMessage', {
        action: 'get',
        chatRoomId: this.data.roomId,
        page,
        pageSize: this.data.pageSize
      }, { showLoading: false });

      console.log('📋 [消息加载] 查询结果:', result);

      if (result.success) {
        const newMessages = result.data.list || [];

        // 处理消息数据，格式化时间和用户信息
        const processedMessages = newMessages.map(msg => ({
          ...msg,
          formattedTime: this.formatMessageTime(msg.createTime),
          isSelf: msg.senderId === this.data.userInfo._id,
          isRecalled: msg.isRecalled || false,
          canRecall: this.canRecallMessage(msg)
        }));

        // 缓存历史消息（不缓存第一页）
        if (!refresh && page > 1 && processedMessages.length > 0) {
          this.setMessageCache(cacheKey, processedMessages);
        }

        // 处理数据
        let messageList;
        if (refresh || page === 1) {
          messageList = processedMessages;
          // 清理旧缓存
          this.clearOldMessageCache();
        } else {
          // 新消息添加到前面（历史消息）
          messageList = [...processedMessages, ...this.data.messageList];
        }

        // 性能优化：限制内存中的消息数量
        const maxMessages = 500; // 最多保留500条消息
        if (messageList.length > maxMessages) {
          messageList = messageList.slice(0, maxMessages);
          console.log(`📦 [性能优化] 限制消息数量为 ${maxMessages} 条`);
        }

        this.setData({
          messageList: messageList,
          page: page,
          hasMore: result.data.hasMore || false
        });

        console.log(`✅ [消息加载] 加载成功: ${messageList.length} 条消息`);

        // 首次加载或刷新时滚动到底部
        if (refresh || page === 1) {
          // 延迟滚动，确保DOM更新完成
          setTimeout(() => {
            this.scrollToBottom();
          }, 100);
        }

        // 智能预加载下一页
        if (result.data.hasMore && page === 1) {
          this.preloadNextPage();
        }

        return Promise.resolve();

      } else {
        console.error('❌ [消息加载] 加载失败:', result.error);
        wx.showToast({
          title: result.error || '加载失败',
          icon: 'none'
        });
        return Promise.reject(new Error(result.error));
      }

    } catch (error) {
      console.error('❌ [消息加载] 加载异常:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
      return Promise.reject(error);
    } finally {
      // 确保隐藏系统加载提示和页面加载状态
      wx.hideLoading();
      this.setData({ loading: false });
    }
  },

  // 发送消息 - 性能优化版
  async sendMessage(e) {
    // 检查是否是禁用状态
    if (e && e.currentTarget && e.currentTarget.dataset.disabled === 'true') {
      return;
    }

    const inputText = this.data.inputText.trim();
    if (!inputText) {
      wx.showToast({
        title: '请输入消息内容',
        icon: 'none'
      });
      return;
    }

    // 🚀 性能优化：立即显示发送状态，提升用户体验
    const tempMessageId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const tempMessage = {
      _id: tempMessageId,
      type: 'text',
      content: inputText,
      isSelf: true,
      createTime: new Date(),
      formattedTime: this.formatTime(new Date()),
      senderInfo: this.data.userInfo,
      isTemp: true, // 标记为临时消息
      isSending: true // 发送中状态
    };

    // 立即添加临时消息到列表，提升用户体验
    const messageList = [...this.data.messageList, tempMessage];
    this.setData({
      messageList,
      inputText: '', // 立即清空输入框
      canSend: false,
      showEmojiPanel: false,
      showAttachmentPanel: false
    });
    this.scrollToBottom();

    try {
      console.log('📤 [消息发送] 开始发送消息 - 性能优化版');
      const startTime = Date.now();

      const result = await API.sendMessage(this.data.roomId, inputText, 'text');

      console.log(`⚡ [性能优化] 消息发送完成，耗时: ${Date.now() - startTime}ms`);

      if (result.success) {
        // 🚀 性能优化：移除临时消息，让实时监听器处理真实消息
        const updatedMessageList = this.data.messageList.filter(msg => msg._id !== tempMessageId);
        this.setData({ messageList: updatedMessageList });

        // 清除该聊天室的撤回状态（发送新消息后撤回状态失效）
        getApp().globalData.recalledMessages.clear(this.data.roomId);

        // 通知聊天列表刷新（发送了新消息）
        this.notifyChatListRefresh(true);

      } else {
        // 发送失败，更新临时消息状态
        const failedMessageList = this.data.messageList.map(msg => {
          if (msg._id === tempMessageId) {
            return { ...msg, isSending: false, isFailed: true };
          }
          return msg;
        });
        this.setData({ messageList: failedMessageList });

        wx.showToast({
          title: result.error || '发送失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('❌ [消息发送] 发送异常:', error);

      // 发送异常，更新临时消息状态
      const failedMessageList = this.data.messageList.map(msg => {
        if (msg._id === tempMessageId) {
          return { ...msg, isSending: false, isFailed: true };
        }
        return msg;
      });
      this.setData({ messageList: failedMessageList });

      wx.showToast({
        title: '发送失败',
        icon: 'none'
      });
    }
  },

  // 输入框内容变化
  onInputChange(e) {
    const inputText = e.detail.value;
    const canSend = inputText && inputText.trim().length > 0;

    this.setData({
      inputText: inputText,
      canSend: canSend
    });

    // 当输入内容变化时，延迟滚动到底部确保输入框可见
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  },

  // 输入框行数变化
  onLineChange(e) {
    console.log('📝 [输入框] 行数变化:', e.detail);
    // 当行数变化时，滚动到底部确保输入框完全可见
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  },

  // 输入框聚焦
  onInputFocus() {
    console.log('📝 [输入框] 聚焦');
    // 聚焦时只关闭附件面板，保持表情包面板打开
    // 这样用户可以在输入文字的同时继续选择表情
    this.setData({
      showAttachmentPanel: false
      // 保持 showEmojiPanel 状态不变，让用户可以继续选择表情
    });
  },

  // 点击消息区域关闭面板
  onMessageAreaTap() {
    if (this.data.showAttachmentPanel || this.data.showEmojiPanel) {
      console.log('📱 [交互] 点击消息区域，关闭面板');
      this.setData({
        showAttachmentPanel: false,
        showEmojiPanel: false
      });

      // 面板关闭后，延迟滚动到底部确保最新消息可见
      setTimeout(() => {
        this.scrollToBottom();
      }, 350); // 等待CSS动画完成
    }
  },

  // 表情包按钮点击
  onEmojiButtonTap() {
    const newShowEmojiPanel = !this.data.showEmojiPanel;
    console.log('🙂 [表情包] 点击表情包按钮，面板状态:', newShowEmojiPanel ? '打开' : '关闭');

    this.setData({
      showEmojiPanel: newShowEmojiPanel,
      showAttachmentPanel: false // 关闭附件面板
    });

    // 面板状态改变后，延迟滚动到底部确保最新消息可见
    setTimeout(() => {
      this.scrollToBottom();
    }, 350); // 等待CSS动画完成
  },

  // 选择表情包
  onSelectEmoji(e) {
    const emoji = e.currentTarget.dataset.emoji;
    console.log('😊 [表情包] 选择表情:', emoji);

    // 将表情添加到输入框
    const currentText = this.data.inputText || '';
    const newText = currentText + emoji;
    const canSend = newText && newText.trim().length > 0;

    this.setData({
      inputText: newText,
      canSend: canSend
    });

    // 保持表情包面板打开，方便用户连续选择表情
    // 用户可以通过以下方式关闭面板：
    // 1. 再次点击表情包按钮
    // 2. 点击输入框聚焦
    // 3. 点击消息区域
    // 4. 发送消息后自动关闭
  },

  // 附件按钮点击
  onAttachmentButtonTap() {
    console.log('📎 [附件] 点击附件按钮');
    this.setData({
      showAttachmentPanel: !this.data.showAttachmentPanel,
      showEmojiPanel: false // 关闭表情包面板
    });

    // 面板状态改变后，延迟滚动到底部确保最新消息可见
    setTimeout(() => {
      this.scrollToBottom();
    }, 350); // 等待CSS动画完成
  },

  // 选择图片（从相册）
  onSelectImage() {
    console.log('🖼️ [附件] 选择图片');
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album'], // 只从相册选择
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.sendImageMessage(tempFilePath);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });

    // 隐藏附件面板
    this.setData({
      showAttachmentPanel: false
    });
  },



  // 拍照
  onSelectCamera() {
    console.log('📷 [附件] 拍照');
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera'], // 只使用相机拍照
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.sendImageMessage(tempFilePath);
      },
      fail: (err) => {
        console.error('拍照失败:', err);
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        });
      }
    });

    // 隐藏附件面板
    this.setData({
      showAttachmentPanel: false
    });
  },

  // 发送图片消息
  async sendImageMessage(imagePath) {
    try {
      console.log('🖼️ [图片发送] 开始发送图片:', imagePath);

      // 🚫 不显示加载状态，使用即时反馈
      // 这里需要先上传图片到云存储，然后发送图片消息
      // 暂时显示提示，后续实现完整的图片发送功能
      wx.showToast({
        title: '图片发送功能开发中',
        icon: 'none'
      });

      // TODO: 实现图片上传和发送逻辑
      // const result = await API.sendMessage(this.data.roomId, imagePath, 'image');

    } catch (error) {
      wx.hideLoading();
      console.error('❌ [图片发送] 发送失败:', error);
      wx.showToast({
        title: '图片发送失败',
        icon: 'none'
      });
    }
  },

  // 滚动到底部
  scrollToBottom() {
    console.log('📍 [滚动] 开始滚动到底部，消息总数:', this.data.messageList.length);

    // 强制滚动方法：使用时间戳确保每次都是不同的值
    const forceScrollToBottom = () => {
      const scrollTop = Date.now() + Math.random() * 1000000; // 确保每次都不同
      this.setData({
        scrollTop: scrollTop,
        scrollToView: ''
      });
      console.log('📍 [滚动] 强制滚动，scrollTop:', scrollTop);
    };

    // 立即执行一次
    forceScrollToBottom();

    // 延迟执行多次，确保滚动生效
    setTimeout(forceScrollToBottom, 100);
    setTimeout(forceScrollToBottom, 300);
    setTimeout(forceScrollToBottom, 500);

    // 最后使用scroll-into-view作为备用
    setTimeout(() => {
      this.setData({
        scrollToView: 'bottom-anchor'
      });
      console.log('📍 [滚动] 备用方案scrollToView: bottom-anchor');
    }, 700);
  },

  // 滚动事件
  onScroll(e) {
    const { scrollTop } = e.detail;

    // 滚动到顶部时加载更多历史消息
    if (scrollTop <= 50 && this.data.hasMore && !this.data.loading) {
      console.log('📥 [历史消息] 加载更多历史消息');
      this.setData({
        page: this.data.page + 1
      });
      this.loadMessages(false);
    }
  },

  // 通知聊天列表刷新
  notifyChatListRefresh(hasNewMessage = false, forceRefresh = false) {
    // 如果不是新消息且不是强制刷新，跳过
    if (!hasNewMessage && !forceRefresh) {
      console.log('🔄 [列表刷新] 无新消息且非强制刷新，跳过刷新');
      return;
    }

    console.log('🔄 [列表刷新] 开始通知聊天列表刷新');

    try {
      const pages = getCurrentPages();
      console.log('🔄 [列表刷新] 当前页面栈:', pages.map(p => p.route));

      const chatListPage = pages.find(page =>
        page.route === 'pages/chat/list/list'
      );

      if (chatListPage) {
        console.log('🔄 [列表刷新] 找到聊天列表页面，检查方法:', {
          hasLoadChatListSilently: typeof chatListPage.loadChatListSilently === 'function',
          hasForceRefresh: typeof chatListPage.forceRefresh === 'function'
        });

        if (chatListPage.loadChatListSilently) {
          console.log('🔄 [列表刷新] 调用静默刷新方法');
          setTimeout(() => {
            chatListPage.loadChatListSilently();
          }, 1000); // 增加延迟，确保聊天室lastMessage字段完全更新
        } else if (chatListPage.forceRefresh) {
          console.log('🔄 [列表刷新] 调用强制刷新方法');
          setTimeout(() => {
            chatListPage.forceRefresh();
          }, 1000);
        } else {
          console.log('🔄 [列表刷新] 聊天列表页面没有可用的刷新方法');
        }
      } else {
        // 如果找不到聊天列表页面，设置全局标记
        console.log('🔄 [列表刷新] 未找到聊天列表页面，设置全局刷新标记');
        getApp().globalData.needRefreshChatList = true;
      }

      // 额外保险：总是设置全局标记
      console.log('🔄 [列表刷新] 设置全局刷新标记作为备用');
      getApp().globalData.needRefreshChatList = true;

    } catch (error) {
      console.error('❌ [列表刷新] 通知失败:', error);
      // 出错时也设置全局标记
      getApp().globalData.needRefreshChatList = true;
    }
  },

  // 查看订单详情
  viewOrderDetail() {
    if (this.data.orderNo) {
      wx.navigateTo({
        url: `/order-package/pages/detail/detail?id=${this.data.orderNo}`
      });
    } else {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none'
      });
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '聊天室',
      path: `/chat-package/pages/room/room?roomId=${this.data.roomId}&orderNo=${this.data.orderNo}`
    };
  },

  // 启动消息监听器
  startMessageWatcher() {
    if (!this.data.roomId || this.data.isWatcherActive) {
      console.log('⚠️ [实时监听] 监听器已启动或房间ID无效，跳过启动');
      return;
    }

    try {
      const db = wx.cloud.database();

      // 监听当前聊天室的消息变更
      const watcher = db.collection('messages')
        .where({
          chatRoomId: this.data.roomId
        })
        .orderBy('createTime', 'desc')
        .limit(50) // 限制监听最新50条消息
        .watch({
          onChange: (snapshot) => {
            // 确保页面仍然存在且监听器仍然活跃
            if (this.data && this.data.isWatcherActive) {
              this.handleMessageChange(snapshot);
            }
          },
          onError: (error) => {
            console.error('监听器错误:', error);

            // 更新状态
            if (this.data) {
              this.setData({
                isWatcherActive: false,
                messageWatcher: null
              });
            }

            // 监听失败时，启用轮询模式作为备用方案
            this.startPollingMode();
          }
        });

      // 保存监听器引用并更新状态
      this.setData({
        messageWatcher: watcher,
        isWatcherActive: true
      });

    } catch (error) {
      console.error('❌ [实时监听] 启动监听器失败:', error);
      this.setData({
        isWatcherActive: false,
        messageWatcher: null
      });
    }
  },

  // 停止消息监听器
  stopMessageWatcher() {
    if (!this.data) {
      return; // 页面已销毁
    }

    if (this.data.messageWatcher || this.data.isWatcherActive) {
      console.log('🛑 [实时监听] 停止消息监听器');

      try {
        // 关闭监听器
        if (this.data.messageWatcher && typeof this.data.messageWatcher.close === 'function') {
          this.data.messageWatcher.close();
        }

        // 清理状态
        this.setData({
          messageWatcher: null,
          isWatcherActive: false
        });

        console.log('✅ [实时监听] 监听器已停止');
      } catch (error) {
        console.error('❌ [实时监听] 停止监听器失败:', error);

        // 即使出错也要清理状态
        try {
          this.setData({
            messageWatcher: null,
            isWatcherActive: false
          });
        } catch (setDataError) {
          console.error('❌ [实时监听] 清理状态失败:', setDataError);
        }
      }
    }
  },

  // 处理消息变更事件
  handleMessageChange(snapshot) {
    console.log('📨 [实时监听] 收到消息变更:', snapshot);

    // 如果是初始化数据，不处理（避免与loadMessages冲突）
    if (snapshot.type === 'init') {
      console.log('📋 [实时监听] 跳过初始化数据');
      return;
    }

    // 处理消息变更（新增和更新）
    const newMessages = [];
    const updatedMessages = [];

    snapshot.docChanges.forEach(change => {
      console.log('🔍 [实时监听] 处理变更:', {
        queueType: change.queueType,
        dataType: change.dataType,
        messageId: change.doc._id,
        isRecalled: change.doc.isRecalled
      });

      console.log('🔍 [实时监听] 开始处理变更:', change.queueType);

      if (change.queueType === 'enqueue' || change.queueType === 'update') {
        const messageData = change.doc;
        console.log('🔍 [实时监听] 消息原始数据:', {
          _id: messageData._id,
          content: messageData.content,
          isRecalled: messageData.isRecalled,
          type: messageData.type
        });

        // 格式化消息数据
        const formattedMessage = {
          _id: messageData._id,
          chatRoomId: messageData.chatRoomId,
          senderId: messageData.senderId,
          senderInfo: messageData.senderInfo,
          content: messageData.content,
          type: messageData.type,
          createTime: messageData.createTime,
          formattedTime: this.formatMessageTime(messageData.createTime),
          isSelf: messageData.senderId === (this.data.userInfo?._id || ''),
          isRecalled: messageData.isRecalled || false,
          canRecall: this.canRecallMessage(messageData)
        };

        console.log('🔍 [实时监听] 格式化后消息:', {
          _id: formattedMessage._id,
          content: formattedMessage.content,
          isRecalled: formattedMessage.isRecalled,
          type: formattedMessage.type
        });

        // 处理撤回消息的显示
        if (formattedMessage.isRecalled) {
          console.log('🔍 [实时监听] 处理撤回消息显示:', messageData._id);
          formattedMessage.content = '[消息已撤回]';
          formattedMessage.type = 'recalled';
          console.log('🔍 [实时监听] 撤回消息处理完成:', formattedMessage.content);
        }

        // 检查消息是否已存在
        const existingIndex = this.data.messageList.findIndex(msg => msg._id === messageData._id);

        console.log('🔍 [实时监听] 消息处理判断:', {
          dataType: change.dataType,
          existingIndex: existingIndex,
          messageId: messageData._id,
          isRecalled: formattedMessage.isRecalled
        });

        if (change.dataType === 'update') {
          // 消息更新（包括撤回）
          console.log('🔄 [实时监听] 准备添加更新消息:', formattedMessage._id, formattedMessage.isRecalled);
          updatedMessages.push(formattedMessage);
          console.log('🔄 [实时监听] 消息更新已添加:', formattedMessage._id, formattedMessage.isRecalled ? '已撤回' : '更新');
          console.log('🔄 [实时监听] 当前updatedMessages长度:', updatedMessages.length);
        } else if (change.dataType === 'add' && existingIndex === -1) {
          // 新消息
          newMessages.push(formattedMessage);
          console.log('📨 [实时监听] 新消息:', formattedMessage.content);
        } else if (existingIndex !== -1) {
          // 已存在的消息，当作更新处理
          updatedMessages.push(formattedMessage);
          console.log('🔄 [实时监听] 已存在消息更新:', formattedMessage._id);
        } else {
          // 兜底处理：如果dataType不明确，根据是否存在来判断
          if (existingIndex === -1) {
            newMessages.push(formattedMessage);
            console.log('📨 [实时监听] 新消息(兜底):', formattedMessage.content);
          } else {
            updatedMessages.push(formattedMessage);
            console.log('🔄 [实时监听] 消息更新(兜底):', formattedMessage._id);
          }
        }
      } else {
        console.log('⚠️ [实时监听] 跳过不支持的变更类型:', change.queueType);
      }
    });

    // 处理消息更新
    let updatedMessageList = [...this.data.messageList];

    // 处理更新的消息（如撤回）
    console.log('🔍 [实时监听] 准备处理更新消息，数量:', updatedMessages.length);
    if (updatedMessages.length > 0) {
      updatedMessages.forEach(updatedMsg => {
        console.log('🔍 [实时监听] 处理更新消息:', updatedMsg._id, '撤回状态:', updatedMsg.isRecalled);
        const existingIndex = updatedMessageList.findIndex(msg => msg._id === updatedMsg._id);
        console.log('🔍 [实时监听] 在本地列表中的位置:', existingIndex);
        if (existingIndex !== -1) {
          // 更新现有消息
          console.log('🔍 [实时监听] 更新前:', updatedMessageList[existingIndex].content, '撤回状态:', updatedMessageList[existingIndex].isRecalled);
          updatedMessageList[existingIndex] = updatedMsg;
          console.log('🔍 [实时监听] 更新后:', updatedMessageList[existingIndex].content, '撤回状态:', updatedMessageList[existingIndex].isRecalled);
          console.log('✅ [实时监听] 消息已更新:', updatedMsg._id);
        } else {
          console.log('⚠️ [实时监听] 未找到要更新的消息:', updatedMsg._id);
        }
      });
    }

    // 如果有新消息，添加到列表并滚动到底部
    if (newMessages.length > 0) {
      // 按时间排序新消息
      newMessages.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));

      newMessages.forEach(newMsg => {
        // 找到插入位置（按时间顺序）
        let insertIndex = updatedMessageList.length;
        for (let i = updatedMessageList.length - 1; i >= 0; i--) {
          if (new Date(updatedMessageList[i].createTime) <= new Date(newMsg.createTime)) {
            insertIndex = i + 1;
            break;
          }
        }
        updatedMessageList.splice(insertIndex, 0, newMsg);
      });

      // 通知聊天列表刷新（有新消息）
      this.notifyChatListRefresh(true);

      console.log(`✅ [实时监听] 添加了 ${newMessages.length} 条新消息`);
    }

    // 如果有任何变更（新消息或更新），更新UI
    if (newMessages.length > 0 || updatedMessages.length > 0) {
      this.setData({
        messageList: updatedMessageList
      });

      // 如果有新消息，滚动到底部
      if (newMessages.length > 0) {
        // 延迟滚动到底部，确保DOM更新完成
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);

        // 再次延迟滚动，确保滚动到最新消息
        setTimeout(() => {
          this.scrollToBottom();
        }, 300);
      }

      // 如果有消息更新（如撤回），通知聊天列表刷新
      if (updatedMessages.length > 0) {
        // 检查是否有撤回消息
        const hasRecalledMessage = updatedMessages.some(msg => msg.isRecalled);
        if (hasRecalledMessage) {
          // 撤回消息需要强制刷新聊天列表
          console.log('🔄 [实时监听] 检测到撤回消息，强制刷新聊天列表');
          this.notifyChatListRefresh(false, true); // 强制刷新
        } else {
          // 其他更新可能是新消息
          this.notifyChatListRefresh(true);
        }
        console.log(`✅ [实时监听] 更新了 ${updatedMessages.length} 条消息`);
      }
    }
  },

  // 页面卸载
  onUnload() {
    console.log('📱 [页面状态] 页面卸载，清理资源');

    // 停止消息监听器
    this.stopMessageWatcher();

    // 清除活跃聊天室状态
    app.globalData.setActiveChatRoom(null);

    // 页面卸载时不需要刷新聊天列表，避免不必要的加载
    // this.notifyChatListRefresh();

    // 停止轮询模式
    this.stopPollingMode();

    // 清理所有引用，防止内存泄漏
    this.data.messageWatcher = null;
    this.data.messageList = [];
  },

  // 判断消息是否可以撤回
  canRecallMessage(message) {
    if (!message || !this.data.userInfo) {
      return false;
    }

    // 只能撤回自己发送的消息
    if (message.senderId !== this.data.userInfo._id) {
      return false;
    }

    // 已撤回的消息不能再次撤回
    if (message.isRecalled) {
      return false;
    }

    // 检查时间限制：2分钟内可撤回
    const now = new Date();
    const messageTime = new Date(message.createTime);
    const timeDiff = (now - messageTime) / 1000 / 60; // 分钟

    return timeDiff <= 2;
  },

  // 消息长按事件
  onMessageLongPress(e) {
    const { messageId, messageIndex, isSelf, isRecalled, createTime } = e.currentTarget.dataset;

    console.log('📱 [消息操作] 长按消息:', { messageId, messageIndex, isSelf, isRecalled });

    // 只有自己发送的消息才能操作
    if (!isSelf) {
      return;
    }

    // 已撤回的消息不能再操作
    if (isRecalled) {
      return;
    }

    // 检查是否可以撤回
    const message = this.data.messageList[messageIndex];
    const canRecall = this.canRecallMessage(message);

    const itemList = [];
    if (canRecall) {
      itemList.push('撤回消息');
    }
    itemList.push('复制消息');

    if (itemList.length === 0) {
      wx.showToast({
        title: '暂无可用操作',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        const selectedAction = itemList[res.tapIndex];

        if (selectedAction === '撤回消息') {
          this.recallMessage(messageId, messageIndex);
        } else if (selectedAction === '复制消息') {
          this.copyMessage(message);
        }
      },
      fail: (err) => {
        console.log('用户取消操作');
      }
    });
  },

  // 撤回消息
  async recallMessage(messageId, messageIndex) {
    try {
      wx.showLoading({
        title: '撤回中...',
        mask: true
      });

      console.log('🔄 [消息撤回] 开始撤回消息:', messageId);
      console.log('🔄 [消息撤回] 聊天室ID:', this.data.roomId);

      const result = await API.recallMessage(messageId, this.data.roomId);

      console.log('🔄 [消息撤回] 云函数返回结果:', result);

      if (result.success) {
        console.log('🎯 [消息撤回] 开始处理撤回成功逻辑');

        try {
          // 更新本地消息状态
          console.log('🔄 [消息撤回] 更新本地消息状态');
          const messageList = [...this.data.messageList];
          if (messageList[messageIndex]) {
            messageList[messageIndex] = {
              ...messageList[messageIndex],
              isRecalled: true,
              content: '[消息已撤回]',
              type: 'recalled',
              canRecall: false
            };

            this.setData({
              messageList: messageList
            });
            console.log('✅ [消息撤回] 本地消息状态已更新');
          }

          wx.showToast({
            title: '撤回成功',
            icon: 'success'
          });

          console.log('✅ [消息撤回] 撤回成功');

          // 获取被撤回消息的时间
          console.log('🔄 [消息撤回] 获取被撤回消息时间');
          const recalledMessage = this.data.messageList[messageIndex];
          const messageTime = recalledMessage ? recalledMessage.createTime : null;
          console.log('🔄 [消息撤回] 消息时间:', messageTime);

          // 使用全局状态管理标记撤回（这会自动触发聊天列表更新）
          console.log('🔄 [消息撤回] 标记全局撤回状态');
          getApp().globalData.recalledMessages.markAsRecalled(this.data.roomId, '[消息已撤回]', messageTime);

          // 强制刷新聊天列表以确保撤回状态正确显示
          console.log('🔄 [消息撤回] 强制刷新聊天列表');
          this.notifyChatListRefresh(false, true);

          console.log('✅ [消息撤回] 撤回成功处理完成');
        } catch (error) {
          console.error('❌ [消息撤回] 撤回成功处理异常:', error);
          throw error;
        }

      } else {
        wx.showToast({
          title: result.error || '撤回失败',
          icon: 'none'
        });
        console.error('❌ [消息撤回] 撤回失败:', result.error);
      }

    } catch (error) {
      console.error('❌ [消息撤回] 撤回异常:', error);
      wx.showToast({
        title: '撤回失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 复制消息
  copyMessage(message) {
    if (!message || !message.content) {
      wx.showToast({
        title: '无法复制此消息',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: message.content,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 消息缓存相关方法
  getMessageCache(key) {
    try {
      if (!this.messageCache) {
        this.messageCache = new Map();
      }
      return this.messageCache.get(key);
    } catch (error) {
      console.error('📦 [消息缓存] 获取缓存失败:', error);
      return null;
    }
  },

  setMessageCache(key, messages) {
    try {
      if (!this.messageCache) {
        this.messageCache = new Map();
      }

      // 限制缓存大小
      if (this.messageCache.size >= 10) {
        const firstKey = this.messageCache.keys().next().value;
        this.messageCache.delete(firstKey);
      }

      this.messageCache.set(key, {
        messages: messages,
        timestamp: Date.now()
      });

      console.log('📦 [消息缓存] 缓存已保存:', key);
    } catch (error) {
      console.error('📦 [消息缓存] 保存缓存失败:', error);
    }
  },

  processCachedMessages(cachedData, page) {
    const { messages } = cachedData;
    const messageList = [...messages, ...this.data.messageList];

    this.setData({
      messageList: messageList,
      page: page,
      loading: false
    });
  },

  clearOldMessageCache() {
    try {
      if (!this.messageCache) return;

      const now = Date.now();
      const maxAge = 5 * 60 * 1000; // 5分钟

      for (const [key, value] of this.messageCache.entries()) {
        if (now - value.timestamp > maxAge) {
          this.messageCache.delete(key);
        }
      }

      console.log('📦 [消息缓存] 清理过期缓存完成');
    } catch (error) {
      console.error('📦 [消息缓存] 清理缓存失败:', error);
    }
  },

  // 智能预加载下一页
  preloadNextPage() {
    if (this.preloadTimer) {
      clearTimeout(this.preloadTimer);
    }

    // 延迟2秒预加载
    this.preloadTimer = setTimeout(() => {
      if (this.data.hasMore && !this.data.loading) {
        console.log('🚀 [智能预加载] 预加载下一页消息');
        this.setData({
          page: this.data.page + 1
        });
        this.loadMessages(false);
      }
    }, 2000);
  },

  // 初始化图片优化器
  initImageOptimizer() {
    try {
      // 清理过期缓存
      imageOptimizer.clearExpiredCache();

      // 预加载聊天室中的图片
      this.preloadChatImages();

      console.log('🖼️ [图片优化] 初始化完成');
    } catch (error) {
      console.error('🖼️ [图片优化] 初始化失败:', error);
    }
  },

  // 预加载聊天室图片
  async preloadChatImages() {
    try {
      // 获取最近的图片消息
      const imageMessages = this.data.messageList
        .filter(msg => msg.type === 'image' && msg.content)
        .slice(0, 10); // 只预加载最近10张图片

      if (imageMessages.length > 0) {
        const imageUrls = imageMessages.map(msg => msg.content);
        console.log('🚀 [图片预加载] 开始预加载聊天图片:', imageUrls.length);

        // 异步预加载，不阻塞主流程
        imageOptimizer.preloadImages(imageUrls).catch(error => {
          console.error('🖼️ [图片预加载] 失败:', error);
        });
      }
    } catch (error) {
      console.error('🖼️ [图片预加载] 异常:', error);
    }
  },

  // 优化图片显示
  async optimizeImageDisplay(imageUrl) {
    try {
      // 检查缓存
      const cachedImage = imageOptimizer.getCacheImage(imageUrl);
      if (cachedImage) {
        return cachedImage;
      }

      // 智能压缩图片
      const optimizedImage = await imageOptimizer.smartCompress(imageUrl);
      return optimizedImage;
    } catch (error) {
      console.error('🖼️ [图片优化] 优化失败:', error);
      return imageUrl; // 返回原图
    }
  },

  // 处理图片消息发送
  async handleImageMessage(imagePath) {
    try {
      // 🚫 不显示加载状态，使用即时反馈机制
      console.log('🖼️ [图片处理] 开始处理图片:', imagePath);

      // 智能压缩图片
      const optimizedPath = await imageOptimizer.smartCompress(imagePath);

      console.log('🖼️ [图片处理] 压缩完成:', {
        original: imagePath,
        optimized: optimizedPath
      });

      // 发送压缩后的图片
      await this.sendMessage(optimizedPath, 'image');

      wx.hideLoading();
    } catch (error) {
      console.error('🖼️ [图片处理] 处理失败:', error);
      wx.hideLoading();

      // 处理失败时发送原图
      try {
        await this.sendMessage(imagePath, 'image');
      } catch (sendError) {
        wx.showToast({
          title: '图片发送失败',
          icon: 'none'
        });
      }
    }
  },

  // 强制刷新聊天列表（用于撤回消息等重要更新）
  forceRefreshChatList() {
    console.log('🔄 [强制刷新] 开始强制刷新聊天列表');

    try {
      const pages = getCurrentPages();
      const chatListPage = pages.find(page => page.route === 'pages/chat/list/list');

      if (chatListPage) {
        console.log('🔄 [强制刷新] 找到聊天列表页面，执行强制刷新');

        // 优先使用强制刷新方法
        if (chatListPage.forceRefresh) {
          setTimeout(() => {
            chatListPage.forceRefresh();
          }, 500);
        } else if (chatListPage.loadChatListSilently) {
          setTimeout(() => {
            chatListPage.loadChatListSilently();
          }, 500);
        } else if (chatListPage.loadChatList) {
          setTimeout(() => {
            chatListPage.loadChatList();
          }, 500);
        }
      } else {
        console.log('🔄 [强制刷新] 未找到聊天列表页面');
      }

      // 设置全局刷新标记
      getApp().globalData.needRefreshChatList = true;

    } catch (error) {
      console.error('❌ [强制刷新] 刷新失败:', error);
      getApp().globalData.needRefreshChatList = true;
    }
  },

  // 直接更新聊天列表中的最后消息显示
  updateChatListLastMessage(newContent) {
    console.log('🔄 [直接更新] 更新聊天列表最后消息:', newContent);
    console.log('🔄 [直接更新] 当前聊天室ID:', this.data.roomId);

    try {
      const pages = getCurrentPages();
      console.log('🔄 [直接更新] 当前页面栈:', pages.map(p => p.route));

      const chatListPage = pages.find(page => page.route === 'pages/chat/list/list');

      if (chatListPage) {
        console.log('🔄 [直接更新] 找到聊天列表页面');
        console.log('🔄 [直接更新] 聊天列表数据存在:', !!chatListPage.data?.chatList);
        console.log('🔄 [直接更新] 聊天列表长度:', chatListPage.data?.chatList?.length || 0);

        if (chatListPage.data && chatListPage.data.chatList) {
          const chatList = [...chatListPage.data.chatList];
          console.log('🔄 [直接更新] 开始查找当前聊天室:', this.data.roomId);

          const currentRoomIndex = chatList.findIndex(chat => {
            console.log('🔍 [直接更新] 检查聊天室:', chat._id, '是否匹配:', chat._id === this.data.roomId);
            return chat._id === this.data.roomId;
          });

          console.log('🔄 [直接更新] 聊天室索引:', currentRoomIndex);

          if (currentRoomIndex !== -1) {
            console.log('🔄 [直接更新] 找到当前聊天室，更新最后消息');
            console.log('🔄 [直接更新] 更新前:', chatList[currentRoomIndex].lastMessageDisplay);

            // 更新显示内容
            chatList[currentRoomIndex].lastMessageDisplay = newContent;

            // 更新最后消息对象
            if (chatList[currentRoomIndex].lastMessage) {
              chatList[currentRoomIndex].lastMessage = {
                ...chatList[currentRoomIndex].lastMessage,
                content: newContent,
                type: 'recalled'
              };
            } else {
              chatList[currentRoomIndex].lastMessage = {
                content: newContent,
                type: 'recalled',
                createTime: new Date()
              };
            }

            console.log('🔄 [直接更新] 更新后:', chatList[currentRoomIndex].lastMessageDisplay);

            // 更新页面数据
            chatListPage.setData({
              chatList: chatList
            });

            console.log('✅ [直接更新] 聊天列表最后消息已更新');
          } else {
            console.log('⚠️ [直接更新] 未找到当前聊天室');
            console.log('🔍 [直接更新] 所有聊天室ID:', chatList.map(chat => chat._id));
          }
        } else {
          console.log('⚠️ [直接更新] 聊天列表数据不存在');
        }
      } else {
        console.log('⚠️ [直接更新] 未找到聊天列表页面');
      }

    } catch (error) {
      console.error('❌ [直接更新] 更新失败:', error);
    }
  },

  // 专门针对撤回消息的强制更新（绕过智能合并）
  forceUpdateChatListForRecall(recallContent) {
    console.log('🚀 [撤回专用] 强制更新聊天列表为撤回状态:', recallContent);

    try {
      const pages = getCurrentPages();
      const chatListPage = pages.find(page => page.route === 'pages/chat/list/list');

      if (chatListPage && chatListPage.data && chatListPage.data.chatList) {
        const chatList = [...chatListPage.data.chatList];
        const currentRoomIndex = chatList.findIndex(chat => chat._id === this.data.roomId);

        if (currentRoomIndex !== -1) {
          console.log('🚀 [撤回专用] 找到目标聊天室，强制覆盖');

          // 强制覆盖所有相关字段
          chatList[currentRoomIndex].lastMessageDisplay = recallContent;
          chatList[currentRoomIndex].lastMessage = {
            content: recallContent,
            type: 'recalled',
            createTime: new Date(),
            messageId: 'recalled_' + Date.now()
          };

          // 设置特殊标记，防止被智能合并覆盖
          chatList[currentRoomIndex]._forceRecalled = true;
          chatList[currentRoomIndex]._recallTime = Date.now();

          console.log('🚀 [撤回专用] 更新前:', chatList[currentRoomIndex].lastMessageDisplay);

          // 立即更新页面数据
          chatListPage.setData({
            chatList: chatList
          });

          console.log('✅ [撤回专用] 强制更新完成');

          // 额外保险：直接操作DOM（如果setData不生效）
          setTimeout(() => {
            this.forceUpdateDOMForRecall(currentRoomIndex, recallContent);
          }, 100);

        } else {
          console.log('⚠️ [撤回专用] 未找到目标聊天室');
        }
      } else {
        console.log('⚠️ [撤回专用] 未找到聊天列表页面');
      }

    } catch (error) {
      console.error('❌ [撤回专用] 强制更新失败:', error);
    }
  },

  // DOM级别的强制更新（最后手段）
  forceUpdateDOMForRecall(roomIndex, recallContent) {
    try {
      console.log('🔧 [DOM更新] 尝试直接操作DOM');
      const pages = getCurrentPages();
      const chatListPage = pages.find(page => page.route === 'pages/chat/list/list');

      if (chatListPage && chatListPage.selectComponent) {
        // 尝试通过组件选择器更新
        const chatItemComponent = chatListPage.selectComponent(`#chat-item-${roomIndex}`);
        if (chatItemComponent) {
          chatItemComponent.setData({
            'item.lastMessageDisplay': recallContent
          });
          console.log('✅ [DOM更新] 组件更新成功');
        }
      }
    } catch (error) {
      console.log('⚠️ [DOM更新] DOM操作失败:', error);
    }
  },

  // 立即更新聊天列表为撤回状态（不等待任何异步操作）
  immediateUpdateChatListForRecall(recallContent) {
    console.log('⚡ [立即更新] 立即更新聊天列表为撤回状态:', recallContent);

    try {
      const pages = getCurrentPages();
      const chatListPage = pages.find(page => page.route === 'pages/chat/list/list');

      if (chatListPage && chatListPage.data && chatListPage.data.chatList) {
        const chatList = [...chatListPage.data.chatList];
        const currentRoomIndex = chatList.findIndex(chat => chat._id === this.data.roomId);

        if (currentRoomIndex !== -1) {
          console.log('⚡ [立即更新] 找到目标聊天室，立即更新');

          // 完全替换该聊天室的消息信息
          chatList[currentRoomIndex].lastMessageDisplay = recallContent;
          chatList[currentRoomIndex].lastMessage = {
            content: recallContent,
            type: 'recalled',
            createTime: new Date(),
            messageId: 'recalled_' + Date.now()
          };

          // 立即更新页面数据
          chatListPage.setData({
            chatList: chatList
          });

          console.log('✅ [立即更新] 立即更新完成');
        } else {
          console.log('⚠️ [立即更新] 未找到目标聊天室');
        }
      } else {
        console.log('⚠️ [立即更新] 未找到聊天列表页面');
      }

    } catch (error) {
      console.error('❌ [立即更新] 立即更新失败:', error);
    }
  },

  // 设置永久撤回标记
  setPermanentRecallMark(recallContent) {
    console.log('🔒 [永久标记] 设置永久撤回标记');

    // 在本地存储中保存撤回标记
    try {
      wx.setStorageSync(`recall_${this.data.roomId}`, {
        content: recallContent,
        timestamp: Date.now()
      });

      // 设置全局标记
      if (!getApp().globalData.permanentRecallRooms) {
        getApp().globalData.permanentRecallRooms = {};
      }
      getApp().globalData.permanentRecallRooms[this.data.roomId] = {
        content: recallContent,
        timestamp: Date.now()
      };

      console.log('✅ [永久标记] 永久撤回标记已设置');

    } catch (error) {
      console.error('❌ [永久标记] 设置永久标记失败:', error);
    }
  },

  // 启动轮询模式（实时监听器失败时的备用方案）
  startPollingMode() {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer);
    }

    console.log('🔄 [轮询模式] 启动消息轮询检测');

    // 记录最后检查的时间
    this.lastPollingTime = new Date();

    // 每3秒检查一次消息更新
    this.pollingTimer = setInterval(() => {
      if (this.data && this.data.roomId) {
        this.checkMessageUpdates();
      } else {
        // 页面已销毁，停止轮询
        this.stopPollingMode();
      }
    }, 3000);
  },

  // 停止轮询模式
  stopPollingMode() {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer);
      this.pollingTimer = null;
      console.log('⏹️ [轮询模式] 已停止轮询');
    }
  },

  // 检查消息更新
  async checkMessageUpdates() {
    try {
      // 查询最近更新的消息
      const result = await API.callFunction('chatMessage', {
        action: 'get',
        chatRoomId: this.data.roomId,
        page: 1,
        pageSize: 10
      }, { showLoading: false });

      if (result.success && result.data.list.length > 0) {
        // 检查是否有消息状态变化（如撤回）
        this.handlePollingUpdates(result.data.list);
      }

    } catch (error) {
      console.error('❌ [轮询模式] 检查更新失败:', error);
    }
  },

  // 处理轮询检测到的消息更新
  handlePollingUpdates(serverMessages) {
    let hasUpdates = false;
    let updatedMessageList = [...this.data.messageList];

    // 检查服务器消息与本地消息的差异
    serverMessages.forEach(serverMsg => {
      const localIndex = updatedMessageList.findIndex(msg => msg._id === serverMsg._id);

      if (localIndex !== -1) {
        const localMsg = updatedMessageList[localIndex];

        // 检查撤回状态是否发生变化
        if (localMsg.isRecalled !== serverMsg.isRecalled) {
          console.log('🔄 [轮询模式] 检测到消息状态变化:', serverMsg._id, serverMsg.isRecalled ? '已撤回' : '恢复');

          // 更新消息状态
          const updatedMsg = {
            ...localMsg,
            isRecalled: serverMsg.isRecalled || false,
            content: serverMsg.isRecalled ? '[消息已撤回]' : serverMsg.content,
            type: serverMsg.isRecalled ? 'recalled' : serverMsg.type,
            canRecall: this.canRecallMessage(serverMsg)
          };

          updatedMessageList[localIndex] = updatedMsg;
          hasUpdates = true;
        }
      }
    });

    // 如果有更新，刷新UI
    if (hasUpdates) {
      console.log('✅ [轮询模式] 更新UI，发现消息状态变化');
      this.setData({
        messageList: updatedMessageList
      });

      // 通知聊天列表刷新
      this.notifyChatListRefresh(true);
    }
  }

});
