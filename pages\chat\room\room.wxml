<!--聊天室页面 - 科技主题版-->
<navigation-bar title="聊天" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 返回按钮 - 与订单详情页面保持一致 -->
<back-button position="top-left" size="normal" back-type="auto"></back-button>

<!-- 科技感装饰背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3]}}" wx:key="*this"></view>
  </view>
</view>

<view class="container page-with-custom-nav">
  <!-- 顶部工具栏 -->
  <view class="toolbar" wx:if="{{orderNo}}">
    <button class="toolbar-button" bindtap="viewOrderDetail">
      查看订单
    </button>
  </view>

  <!-- 消息列表 -->
  <scroll-view
    class="message-list {{showEmojiPanel ? 'emoji-panel-open' : ''}} {{showAttachmentPanel ? 'attachment-panel-open' : ''}}"
    scroll-y="{{true}}"
    scroll-into-view="{{scrollToView}}"
    scroll-top="{{scrollTop}}"
    scroll-with-animation="{{false}}"
    enable-back-to-top="{{false}}"
    enhanced="{{true}}"
    show-scrollbar="{{false}}"
    bindscroll="onScroll"
    bindtap="onMessageAreaTap"
  >
    <!-- 历史消息加载提示 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-more-text">{{loading ? '加载中...' : '下拉加载更多'}}</text>
    </view>

    <!-- 消息项 -->
    <view
      class="message-item {{item.isSelf ? 'self' : 'other'}}"
      wx:for="{{messageList}}"
      wx:key="_id"
      id="msg-{{index}}"
    >
      <!-- 对方消息 -->
      <view class="message-content other-message with-nickname" wx:if="{{!item.isSelf}}">
        <view class="avatar-container">
          <view class="nickname">{{item.senderInfo.nickName || '未知用户'}}</view>
          <image wx:if="{{item.senderInfo.avatarUrl}}" class="avatar" src="{{item.senderInfo.avatarUrl}}" mode="aspectFill" />
          <view wx:else class="avatar default-avatar">
            <text class="avatar-text">👤</text>
          </view>
        </view>
        <view class="message-bubble">
          <view class="message-text">{{item.content}}</view>
          <view class="message-time">{{item.formattedTime}}</view>
        </view>
      </view>

      <!-- 自己的消息 -->
      <view class="message-content self-message with-nickname" wx:if="{{item.isSelf}}">
        <view
          class="message-bubble {{item.isRecalled ? 'recalled' : ''}}"
          bindlongpress="onMessageLongPress"
          data-message-id="{{item._id}}"
          data-message-index="{{index}}"
          data-is-self="{{item.isSelf}}"
          data-is-recalled="{{item.isRecalled}}"
          data-create-time="{{item.createTime}}"
        >
          <view class="message-text" wx:if="{{!item.isRecalled}}">{{item.content}}</view>
          <view class="message-text recalled-text" wx:else>
            <text class="recalled-icon">🚫</text>
            {{item.content}}
          </view>
          <view class="message-time">{{item.formattedTime}}</view>
        </view>
        <view class="avatar-container">
          <view class="nickname">{{userInfo.nickName || '我'}}</view>
          <image wx:if="{{userInfo.avatarUrl}}" class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill" />
          <view wx:else class="avatar default-avatar">
            <text class="avatar-text">👤</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-messages" wx:if="{{messageList.length === 0 && !loading}}">
      <text class="empty-text">开始聊天吧~</text>
    </view>

    <!-- 底部占位元素，用于滚动定位 -->
    <view id="bottom-anchor" class="bottom-anchor"></view>
  </scroll-view>


</view>

<!-- 输入区域 - 固定在底部 -->
<view class="input-area">
  <view class="input-container">
    <!-- 语音按钮（可选，暂时隐藏） -->
    <view class="voice-button" wx:if="{{false}}" bindtap="onVoiceButtonTap">
      <image class="voice-icon" src="/images/icons/voice.png" mode="aspectFit" />
    </view>

    <!-- 输入框 -->
    <textarea
      class="message-input"
      placeholder="输入消息..."
      value="{{inputText}}"
      bindinput="onInputChange"
      bindfocus="onInputFocus"
      bindblur="onInputBlur"
      bindlinechange="onLineChange"
      auto-height="{{true}}"
      show-confirm-bar="{{false}}"
      cursor-spacing="20"
      max-height="{{240}}"
      focus="{{inputFocus}}"
      hold-keyboard="{{keepKeyboard}}"
      adjust-position="{{false}}"
      disable-default-padding="{{true}}"
    />

    <!-- 表情包按钮 -->
    <view class="emoji-button" bindtap="onEmojiButtonTap">
      <text class="emoji-icon">😊</text>
    </view>

    <!-- 附件/发送按钮 -->
    <view class="action-button-container">
      <!-- 附件按钮（无文字时显示） -->
      <view
        class="attachment-button {{canSend ? 'hidden' : 'visible'}}"
        bindtap="onAttachmentButtonTap"
      >
        <text class="attachment-icon">+</text>
      </view>

      <!-- 发送按钮（有文字时显示） -->
      <view
        class="send-button {{canSend ? 'visible' : 'hidden'}}"
        catchtap="{{canSend ? 'sendMessage' : ''}}"
        data-disabled="{{!canSend}}"
        data-keep-focus="true"
      >
        发送
      </view>
    </view>
  </view>

  <!-- 附件选择面板 -->
  <view class="attachment-panel {{showAttachmentPanel ? 'show' : 'hide'}}" wx:if="{{showAttachmentPanel}}">
    <view class="attachment-options">
      <view class="attachment-option" bindtap="onSelectImage">
        <view class="option-icon">
          <text class="option-icon-text">🖼️</text>
        </view>
        <text class="option-text">图片</text>
      </view>
      <view class="attachment-option" bindtap="onSelectCamera">
        <view class="option-icon">
          <text class="option-icon-text">📷</text>
        </view>
        <text class="option-text">拍照</text>
      </view>
    </view>
  </view>

  <!-- 表情包面板 -->
  <view class="emoji-panel {{showEmojiPanel ? 'show' : 'hide'}}" wx:if="{{showEmojiPanel}}">
    <!-- 面板头部 -->
    <view class="emoji-header">
      <text class="emoji-title">选择表情</text>
      <view class="emoji-close-hint">
        <text class="close-hint-text">点击😊或消息区域关闭</text>
      </view>
    </view>

    <scroll-view class="emoji-scroll" scroll-y="{{true}}">
      <view class="emoji-categories">
        <view
          class="emoji-category"
          wx:for="{{emojiList}}"
          wx:key="category"
        >
          <view class="category-title">{{item.category}}</view>
          <view class="emoji-grid">
            <view
              class="emoji-item"
              wx:for="{{item.emojis}}"
              wx:for-item="emoji"
              wx:key="*this"
              data-emoji="{{emoji}}"
              bindtap="onSelectEmoji"
            >
              {{emoji}}
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
