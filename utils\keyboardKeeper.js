/**
 * 键盘保持器 - 专门解决手机端键盘收回问题
 * 使用多种技术手段强制保持键盘显示
 */

class KeyboardKeeper {
  constructor() {
    this.isActive = false;
    this.inputElement = null;
    this.keepAliveTimer = null;
    this.focusCheckTimer = null;
    this.retryCount = 0;
    this.maxRetries = 10;
    
    console.log('⌨️ [键盘保持器] 初始化完成');
  }

  /**
   * 激活键盘保持功能
   */
  activate(page) {
    if (this.isActive) {
      console.log('⌨️ [键盘保持器] 已经激活');
      return;
    }

    this.page = page;
    this.isActive = true;
    
    console.log('⌨️ [键盘保持器] 开始激活');
    
    // 启动多重保持机制
    this.startKeepAlive();
    this.startFocusMonitor();
    this.interceptBlurEvents();
    
    console.log('⌨️ [键盘保持器] 激活完成');
  }

  /**
   * 停用键盘保持功能
   */
  deactivate() {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;
    
    // 清理所有定时器
    if (this.keepAliveTimer) {
      clearInterval(this.keepAliveTimer);
      this.keepAliveTimer = null;
    }
    
    if (this.focusCheckTimer) {
      clearInterval(this.focusCheckTimer);
      this.focusCheckTimer = null;
    }

    console.log('⌨️ [键盘保持器] 已停用');
  }

  /**
   * 启动保活机制
   */
  startKeepAlive() {
    this.keepAliveTimer = setInterval(() => {
      if (this.isActive && this.page) {
        this.maintainKeyboard();
      }
    }, 50); // 每50ms检查一次
  }

  /**
   * 启动焦点监控
   */
  startFocusMonitor() {
    this.focusCheckTimer = setInterval(() => {
      if (this.isActive && this.page) {
        this.checkAndRestoreFocus();
      }
    }, 100); // 每100ms检查一次
  }

  /**
   * 维持键盘显示
   */
  maintainKeyboard() {
    if (!this.page || !this.page.data) {
      return;
    }

    const shouldKeep = this.page.data.keepKeyboard;
    const currentFocus = this.page.data.inputFocus;

    if (shouldKeep && !currentFocus) {
      console.log('⌨️ [键盘保持器] 检测到焦点丢失，立即恢复');
      this.forceRestoreFocus();
    }
  }

  /**
   * 检查并恢复焦点
   */
  checkAndRestoreFocus() {
    if (!this.page || !this.page.data.keepKeyboard) {
      return;
    }

    // 使用选择器查询输入框状态
    const query = wx.createSelectorQuery().in(this.page);
    query.select('.message-input').fields({
      properties: ['focus']
    }).exec((res) => {
      if (res && res[0] && !res[0].focus) {
        console.log('⌨️ [键盘保持器] 选择器检测到失焦，恢复中...');
        this.forceRestoreFocus();
      }
    });
  }

  /**
   * 强制恢复焦点
   */
  forceRestoreFocus() {
    if (!this.page || this.retryCount >= this.maxRetries) {
      return;
    }

    this.retryCount++;
    
    // 方法1: 使用setData
    this.page.setData({
      inputFocus: false
    }, () => {
      setTimeout(() => {
        this.page.setData({
          inputFocus: true,
          keepKeyboard: true
        });
      }, 1);
    });

    // 方法2: 使用原生API
    setTimeout(() => {
      this.focusWithNativeAPI();
    }, 10);

    // 方法3: 使用context API
    setTimeout(() => {
      this.focusWithContext();
    }, 20);

    console.log(`⌨️ [键盘保持器] 强制恢复焦点，尝试次数: ${this.retryCount}`);
  }

  /**
   * 使用原生API聚焦
   */
  focusWithNativeAPI() {
    if (!this.page) return;

    const query = wx.createSelectorQuery().in(this.page);
    query.select('.message-input').context((res) => {
      if (res && res.context && res.context.focus) {
        res.context.focus();
        console.log('⌨️ [键盘保持器] 原生API聚焦成功');
      }
    }).exec();
  }

  /**
   * 使用context聚焦
   */
  focusWithContext() {
    if (!this.page) return;

    try {
      const query = wx.createSelectorQuery().in(this.page);
      query.select('.message-input').context().exec((res) => {
        if (res && res[0] && res[0].context) {
          res[0].context.focus && res[0].context.focus();
        }
      });
    } catch (error) {
      console.error('⌨️ [键盘保持器] context聚焦失败:', error);
    }
  }

  /**
   * 拦截失焦事件
   */
  interceptBlurEvents() {
    // 这个方法在页面级别实现
    console.log('⌨️ [键盘保持器] 失焦事件拦截已设置');
  }

  /**
   * 发送消息时的特殊处理
   */
  onMessageSend() {
    if (!this.isActive || !this.page) {
      return;
    }

    console.log('⌨️ [键盘保持器] 消息发送，启动强化保持');
    
    // 重置重试计数
    this.retryCount = 0;
    
    // 立即设置保持状态
    this.page.setData({
      keepKeyboard: true,
      inputFocus: true
    });

    // 延迟多次尝试恢复
    const delays = [1, 5, 10, 20, 50, 100, 200, 300, 500];
    delays.forEach(delay => {
      setTimeout(() => {
        if (this.isActive) {
          this.forceRestoreFocus();
        }
      }, delay);
    });
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      isActive: this.isActive,
      retryCount: this.retryCount,
      hasKeepAliveTimer: !!this.keepAliveTimer,
      hasFocusCheckTimer: !!this.focusCheckTimer
    };
  }
}

// 创建全局实例
const keyboardKeeper = new KeyboardKeeper();

// 导出方法
module.exports = {
  keyboardKeeper,
  
  // 激活键盘保持
  activate(page) {
    keyboardKeeper.activate(page);
  },
  
  // 停用键盘保持
  deactivate() {
    keyboardKeeper.deactivate();
  },
  
  // 消息发送时调用
  onMessageSend() {
    keyboardKeeper.onMessageSend();
  },
  
  // 获取统计
  getStats() {
    return keyboardKeeper.getStats();
  }
};
