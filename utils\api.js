// API 接口封装
const errorHandler = require('./errorHandler.js');

const app = getApp();

class API {
  constructor() {
    this.baseUrl = '';
  }

  // 调用云函数
  async callFunction(name, data = {}, options = {}) {
    const { showLoading = true, timeout = 10000 } = options;
    let loadingShown = false;

    try {
      if (showLoading) {
        // 安全地调用showLoading
        if (app && app.utils && app.utils.showLoading) {
          app.utils.showLoading();
        } else {
          wx.showLoading({ title: '加载中...' });
        }
        loadingShown = true;
      }

      // 添加超时控制
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), timeout);
      });

      const requestPromise = wx.cloud.callFunction({
        name,
        data
      });

      const result = await Promise.race([requestPromise, timeoutPromise]);

      if (loadingShown) {
        // 安全地调用hideLoading
        if (app && app.utils && app.utils.hideLoading) {
          app.utils.hideLoading();
        } else {
          wx.hideLoading();
        }
      }

      if (result.result) {
        return result.result;
      } else {
        throw new Error('云函数返回数据格式错误');
      }
    } catch (error) {
      if (loadingShown) {
        // 安全地调用hideLoading
        if (app && app.utils && app.utils.hideLoading) {
          app.utils.hideLoading();
        } else {
          wx.hideLoading();
        }
      }

      console.error(`云函数 ${name} 调用失败:`, error);
      
      // 使用错误处理器
      try {
        errorHandler.handle(error, `云函数调用: ${name}`);
      } catch (handlerError) {
        console.error('错误处理器调用失败:', handlerError);
      }

      throw error;
    }
  }

  // ==================== 用户相关接口 ====================
  
  // 用户登录
  async login(userInfo) {
    return await this.callFunction('login', userInfo);
  }

  // 获取用户信息
  async getUserInfo(options = {}) {
    return await this.callFunction('getUserInfo', {}, options);
  }

  // 更新用户信息
  async updateUserInfo(userInfo) {
    return await this.callFunction('updateUserInfo', userInfo);
  }

  // 更新用户设置
  async updateUserSettings(settings) {
    return await this.callFunction('updateUserSettings', { settings });
  }

  // 用户认证
  async verifyUser(data) {
    return await this.callFunction('verifyUser', data);
  }

  // ==================== 订单相关接口 ====================
  
  // 创建订单
  async createOrder(orderData) {
    return await this.callFunction('createOrder', orderData);
  }

  // 获取订单列表
  async getOrderList(params) {
    return await this.callFunction('getOrderList', params, { showLoading: false });
  }

  // 获取抢单大厅订单列表
  async getGrabOrderList(params, options = {}) {
    return await this.callFunction('getGrabOrderList', params, { showLoading: false });
  }

  // 获取订单详情
  async getOrderDetail(orderId) {
    return await this.callFunction('getOrderDetail', { orderId }, { showLoading: false });
  }



  // 获取订单统计数量
  async getOrderCounts() {
    return await this.callFunction('getOrderCounts', {}, { showLoading: false });
  }

  // 接单
  async acceptOrder(orderId) {
    return await this.callFunction('acceptOrder', { orderId });
  }

  // 抢单
  async grabOrder(orderId) {
    console.log('🎯 API.grabOrder 被调用, orderId:', orderId);
    const result = await this.callFunction('acceptOrder', { orderId });
    console.log('📊 acceptOrder 云函数返回结果:', result);
    return result;
  }

  // 预热云函数（减少冷启动）
  async warmupGrabOrder() {
    try {
      console.log('🔥 预热抢单云函数...');
      await this.callFunction('acceptOrder', { action: 'version' });
      console.log('✅ 抢单云函数预热完成');
    } catch (error) {
      console.log('⚠️ 抢单云函数预热失败:', error.message);
    }
  }

  // 取消订单
  async cancelOrder(orderId, reason, userRole = null) {
    // 检查是否为演示模式
    const userInfo = app.globalData.userInfo;
    if (userInfo && userInfo.isDemo) {
      // 演示模式：模拟取消成功
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            message: '演示模式：订单已取消',
            data: {
              orderId: orderId,
              status: 'cancelled'
            }
          });
        }, 500);
      });
    }

    // 如果没有传递 userRole，尝试自动判断
    if (!userRole) {
      try {
        console.log('🔍 [API] 自动判断用户角色...');
        const orderDetailResult = await this.getOrderDetail(orderId);
        if (orderDetailResult.success) {
          userRole = orderDetailResult.userRole;
          console.log('🔍 [API] 自动判断的用户角色:', userRole);
        } else {
          console.error('❌ [API] 获取订单详情失败，无法判断用户角色');
          return {
            success: false,
            error: '无法确定用户角色，请重试'
          };
        }
      } catch (error) {
        console.error('❌ [API] 自动判断用户角色失败:', error);
        return {
          success: false,
          error: '无法确定用户角色，请重试'
        };
      }
    }

    // 正常模式：调用云函数
    console.log('🚀 [API] 准备调用cancelOrder云函数:', { orderId, reason, userRole });
    const result = await this.callFunction('cancelOrder', { orderId, reason, userRole });
    console.log('📋 [API] cancelOrder云函数返回:', result);
    return result;
  }

  // 完成订单
  async completeOrder(orderId) {
    return await this.callFunction('updateOrderStatus', {
      orderId,
      status: 'completed'
    });
  }

  // 完成订单
  async completeOrder(orderId) {
    return await this.callFunction('updateOrderStatus', {
      orderId,
      status: 'completed'
    });
  }

  // ==================== 数据库优化接口 ====================

  // 创建数据库索引
  async createDatabaseIndexes() {
    return await this.callFunction('optimizeDatabase', { action: 'createIndexes' });
  }

  // 分析数据库性能
  async analyzeDatabasePerformance() {
    return await this.callFunction('optimizeDatabase', { action: 'analyzePerformance' });
  }

  // 优化数据库查询
  async optimizeDatabaseQueries() {
    return await this.callFunction('optimizeDatabase', { action: 'optimizeQueries' });
  }

  // ==================== 通知系统接口 ====================

  // 创建通知
  async createNotification(notificationData) {
    return await this.callFunction('notificationManager', {
      action: 'createNotification',
      data: notificationData
    });
  }

  // 获取通知列表
  async getNotificationList(params) {
    return await this.callFunction('notificationManager', {
      action: 'getNotificationList',
      data: params
    });
  }

  // 标记通知为已读
  async markNotificationAsRead(notificationId) {
    return await this.callFunction('notificationManager', {
      action: 'markAsRead',
      data: { notificationId }
    });
  }

  // 标记所有通知为已读
  async markAllNotificationsAsRead() {
    return await this.callFunction('notificationManager', {
      action: 'markAllAsRead'
    });
  }

  // 获取未读通知数量
  async getUnreadNotificationCount() {
    return await this.callFunction('notificationManager', {
      action: 'getUnreadCount'
    });
  }

  // 删除通知
  async deleteNotification(notificationId) {
    return await this.callFunction('notificationManager', {
      action: 'deleteNotification',
      data: { notificationId }
    });
  }

  // 订阅消息
  async subscribeMessage(templateId, page) {
    return await this.callFunction('notificationManager', {
      action: 'subscribeMessage',
      data: { templateId, page }
    });
  }

  // ==================== 信誉系统接口 ====================

  // 获取用户信誉信息
  async getUserReputation(userId) {
    return await this.callFunction('reputationSystem', {
      action: 'getUserReputation',
      data: { userId }
    });
  }

  // 更新用户信誉
  async updateReputation(userId, scoreType, orderId, evaluationData) {
    return await this.callFunction('reputationSystem', {
      action: 'updateReputation',
      data: { userId, scoreType, orderId, evaluationData }
    });
  }

  // 获取信誉历史记录
  async getReputationHistory(userId, page, pageSize) {
    return await this.callFunction('reputationSystem', {
      action: 'getReputationHistory',
      data: { userId, page, pageSize }
    });
  }

  // 获取排行榜
  async getLeaderboard(type, limit) {
    return await this.callFunction('reputationSystem', {
      action: 'getLeaderboard',
      data: { type, limit }
    });
  }

  // 计算订单积分
  async calculateOrderScore(orderId, userId) {
    return await this.callFunction('reputationSystem', {
      action: 'calculateOrderScore',
      data: { orderId, userId }
    });
  }

  // 获取用户徽章
  async getUserBadges(userId) {
    return await this.callFunction('reputationSystem', {
      action: 'getBadges',
      data: { userId }
    });
  }

  // ==================== 安全系统接口 ====================

  // 检查请求频率限制
  async checkRateLimit(operationType, customLimit) {
    return await this.callFunction('securityMiddleware', {
      action: 'checkRateLimit',
      data: { operationType, customLimit }
    });
  }

  // 输入验证
  async validateInput(inputs) {
    return await this.callFunction('securityMiddleware', {
      action: 'validateInput',
      data: { inputs }
    });
  }

  // 数据脱敏
  async maskSensitiveData(dataObject, fieldsToMask) {
    return await this.callFunction('securityMiddleware', {
      action: 'maskSensitiveData',
      data: { dataObject, fieldsToMask }
    });
  }

  // 权限检查
  async checkPermission(permission, resourceId, resourceType) {
    return await this.callFunction('securityMiddleware', {
      action: 'checkPermission',
      data: { permission, resourceId, resourceType }
    });
  }

  // 记录安全事件
  async logSecurityEvent(eventType, operationType, details, severity) {
    return await this.callFunction('securityMiddleware', {
      action: 'logSecurityEvent',
      data: { eventType, operationType, details, severity }
    });
  }

  // 获取安全报告
  async getSecurityReport(timeRange, eventTypes) {
    return await this.callFunction('securityMiddleware', {
      action: 'getSecurityReport',
      data: { timeRange, eventTypes }
    });
  }

  // 开始服务
  async startService(orderId) {
    return await this.callFunction('updateOrderStatus', {
      orderId,
      status: 'in_progress'
    });
  }

  // 开始服务
  async startService(orderId) {
    return await this.callFunction('updateOrderStatus', {
      orderId,
      status: 'in_progress'
    });
  }

  // 修改订单
  async updateOrder(orderId, updateData) {
    return await this.callFunction('updateOrder', { orderId, ...updateData });
  }

  // 获取我的订单
  async getMyOrders(params) {
    return await this.callFunction('getMyOrders', params);
  }

  // 获取我接的订单
  async getAcceptedOrders(params) {
    return await this.callFunction('getAcceptedOrders', params);
  }

  // ==================== 聊天相关接口 ====================
  
  // 聊天室管理
  async createChatRoom(orderId) {
    return await this.callFunction('chatRoom', { action: 'create', orderId });
  }

  async getChatRoomInfo(orderId) {
    return await this.callFunction('chatRoom', { action: 'get', orderId });
  }

  // 聊天列表
  async getChatList(params = {}) {
    return await this.callFunction('chatList', params);
  }

  // 消息管理 - 性能优化版
  async sendMessage(chatRoomId, content, type = 'text') {
    // 引入性能监控
    const performanceMonitor = require('./performanceMonitor');
    const timerId = performanceMonitor.monitorMessageSend(chatRoomId, type);

    try {
      const result = await this.callFunction('chatMessage', {
        action: 'send',
        chatRoomId,
        content,
        type
      });

      // 结束性能监控
      performanceMonitor.endTimer(timerId, result.success);

      return result;
    } catch (error) {
      // 记录失败的性能数据
      performanceMonitor.endTimer(timerId, false, error.message);
      throw error;
    }
  }

  async sendVoiceMessage(chatRoomId, voiceFileId, duration) {
    return await this.callFunction('chatMessage', {
      action: 'send',
      chatRoomId,
      content: voiceFileId,
      type: 'voice',
      duration: duration
    });
  }

  async getMessages(chatRoomId, page = 1, pageSize = 20) {
    return await this.callFunction('chatMessage', {
      action: 'get',
      chatRoomId,
      page,
      pageSize
    });
  }

  async recallMessage(messageId, chatRoomId) {
    return await this.callFunction('chatMessage', {
      action: 'recall',
      messageId,
      chatRoomId
    });
  }

  // 简单删除聊天（软删除）
  async simpleDeleteChat(chatRoomId, action = 'soft') {
    return await this.callFunction('simpleDeleteChat', { chatRoomId, action });
  }

  // ==================== 支付相关接口 ====================

  // 创建充值订单
  async createRecharge(params) {
    return await this.callFunction('createRecharge', params);
  }

  // 创建提现申请
  async createWithdraw(params) {
    return await this.callFunction('createWithdraw', params);
  }

  // 获取提现记录
  async getWithdrawList(params) {
    return await this.callFunction('getWithdrawList', params);
  }

  // 获取交易记录
  async getTransactionList(params) {
    return await this.callFunction('getTransactionList', params);
  }

  // 评价订单
  async evaluateOrder(data) {
    return await this.callFunction('evaluateOrder', data);
  }

  // 提交评价
  async submitEvaluation(data) {
    return await this.callFunction('submitEvaluation', data);
  }

  // 获取评价信息
  async getEvaluationInfo(orderId) {
    return await this.callFunction('getEvaluationInfo', { orderId });
  }

  // 获取用户评价统计
  async getUserEvaluationStats(userId = null, includeDetails = false) {
    return await this.callFunction('getUserEvaluationStats', { userId, includeDetails });
  }

  // 创建支付
  async createPayment(data) {
    return await this.callFunction('createPayment', data);
  }

  // 申请提现
  async withdraw(data) {
    return await this.callFunction('withdraw', data);
  }

  // 获取交易记录
  async getTransactionRecords(params) {
    return await this.callFunction('getTransactionRecords', params);
  }

  // 获取交易列表（使用现有云函数）
  async getTransactionList(params) {
    const { showLoading, ...functionParams } = params;
    const options = showLoading !== undefined ? { showLoading } : {};
    return await this.callFunction('getTransactionList', functionParams, options);
  }

  // 获取钱包统计数据
  async getWalletStats(params = {}) {
    const { showLoading, ...functionParams } = params;
    const options = showLoading !== undefined ? { showLoading } : {};
    return await this.callFunction('getWalletStats', functionParams, options);
  }

  // 上传文件到云存储
  async uploadFile(filePath, cloudPath) {
    try {
      app.utils.showLoading('上传中...');
      const result = await wx.cloud.uploadFile({
        cloudPath,
        filePath
      });
      app.utils.hideLoading();
      return result;
    } catch (error) {
      app.utils.hideLoading();
      app.utils.showError('上传失败');
      throw error;
    }
  }

  // 删除云存储文件
  async deleteFile(fileList) {
    try {
      const result = await wx.cloud.deleteFile({
        fileList
      });
      return result;
    } catch (error) {
      console.error('删除文件失败:', error);
      throw error;
    }
  }

  // 初始化数据库
  async initDatabase(action = 'init') {
    return await this.callFunction('initDatabase', { action });
  }

  // ==================== 通知相关 API ====================

  // 发送通知
  async sendNotification(data) {
    return await this.callFunction('sendNotification', data);
  }

  // 获取通知列表
  async getNotificationList(params) {
    return await this.callFunction('getNotificationList', params);
  }

  // 标记通知已读
  async markNotificationRead(notificationId, markAllRead = false) {
    return await this.callFunction('markNotificationRead', {
      notificationId,
      markAllRead
    });
  }

  // 获取未读通知数量
  async getUnreadNotificationCount() {
    const result = await this.getNotificationList({
      page: 1,
      pageSize: 1,
      unreadOnly: true
    });
    return result.success ? result.data.unreadCount : 0;
  }



  // ==================== 统计相关 API ====================

  // 获取订单统计数据
  async getOrderStatistics(timeRange = 'month', type = 'overview') {
    return await this.callFunction('getOrderStatistics', { timeRange, type });
  }
}

module.exports = new API();
